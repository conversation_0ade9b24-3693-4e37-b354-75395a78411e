import React, { useState, useEffect } from 'react'

function SettingsPanel({
  settings,
  accounts,
  onLoadAccounts,
  onLoadProxies,
  onLoadComments,
  onUpdateSettings,
  onStartAutomation,
  onStopAutomation,
  onPauseAutomation,
  isConnected
}) {
  const [localSettings, setLocalSettings] = useState({
    targetProfile: '',
    videosToWatch: 3,
    watchTimeSeconds: 30,
    maxFollowsPerDay: 50,
    maxFollowsPerSession: 10,
    restBetweenSessions: 3600
  });

  const [isRunning, setIsRunning] = useState(false);

  useEffect(() => {
    if (settings) {
      setLocalSettings(settings);
    }
  }, [settings]);

  const handleSettingChange = (key, value) => {
    setLocalSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSaveSettings = () => {
    onUpdateSettings(localSettings);
  };

  const handleStart = () => {
    if (!localSettings.targetProfile) {
      alert('Vui lòng nhập link profile đối thủ');
      return;
    }

    // Lấy danh sách tài khoản đã sẵn sàng (status = 'ready')
    const readyAccounts = accounts.filter(acc => acc.status === 'ready');

    if (readyAccounts.length === 0) {
      alert('Không có tài khoản nào sẵn sàng. Vui lòng đăng nhập tài khoản trước.');
      return;
    }

    const accountIds = readyAccounts.map(acc => acc.id);
    setIsRunning(true);
    onStartAutomation(accountIds, localSettings.targetProfile);
  };

  const handleStop = () => {
    setIsRunning(false);
    onStopAutomation();
  };

  const handlePause = () => {
    onPauseAutomation();
  };

  // Đếm số tài khoản sẵn sàng
  const readyAccountsCount = accounts.filter(acc => acc.status === 'ready').length;

  return (
    <div className="component-card">
      <div className="component-header">
        Điều khiển & Cài đặt
      </div>

      <div className="component-content">
        {/* Nhóm nút nạp dữ liệu */}
        <div style={{ marginBottom: '24px' }}>
          <h4 style={{ margin: '0 0 12px 0', color: '#374151' }}>Nạp dữ liệu</h4>
          <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
            <button
              className="btn btn-primary"
              onClick={onLoadAccounts}
              disabled={!isConnected}
            >
              📁 Nạp Tài khoản
            </button>
            <button
              className="btn btn-primary"
              onClick={onLoadProxies}
              disabled={!isConnected}
            >
              🌐 Nạp Proxy
            </button>
            <button
              className="btn btn-primary"
              onClick={onLoadComments}
              disabled={!isConnected}
            >
              💬 Nạp Bình luận
            </button>
          </div>
        </div>

        {/* Nhóm hành động chính */}
        <div style={{ marginBottom: '24px' }}>
          <h4 style={{ margin: '0 0 12px 0', color: '#374151' }}>
            Điều khiển ({readyAccountsCount} tài khoản sẵn sàng)
          </h4>
          <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
            <button
              className="btn btn-success"
              onClick={handleStart}
              disabled={!isConnected || isRunning || readyAccountsCount === 0}
              title={readyAccountsCount === 0 ? 'Cần có ít nhất 1 tài khoản sẵn sàng' : ''}
            >
              ▶️ Bắt đầu ({readyAccountsCount})
            </button>
            <button
              className="btn btn-danger"
              onClick={handleStop}
              disabled={!isConnected || !isRunning}
            >
              ⏹️ Dừng hẳn
            </button>
            <button
              className="btn btn-secondary"
              onClick={handlePause}
              disabled={!isConnected || !isRunning}
            >
              ⏸️ Tạm dừng
            </button>
          </div>
        </div>

        {/* Cài đặt kịch bản */}
        <div style={{ marginBottom: '24px' }}>
          <h4 style={{ margin: '0 0 12px 0', color: '#374151' }}>Cài đặt kịch bản</h4>

          <div className="form-group">
            <label className="form-label">Link profile đối thủ</label>
            <input
              type="text"
              className="form-input"
              placeholder="https://www.tiktok.com/@username"
              value={localSettings.targetProfile}
              onChange={(e) => handleSettingChange('targetProfile', e.target.value)}
            />
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
            <div className="form-group">
              <label className="form-label">Số video xem</label>
              <input
                type="number"
                className="form-input"
                min="1"
                max="10"
                value={localSettings.videosToWatch}
                onChange={(e) => handleSettingChange('videosToWatch', parseInt(e.target.value))}
              />
            </div>

            <div className="form-group">
              <label className="form-label">Thời gian xem (giây)</label>
              <input
                type="number"
                className="form-input"
                min="10"
                max="120"
                value={localSettings.watchTimeSeconds}
                onChange={(e) => handleSettingChange('watchTimeSeconds', parseInt(e.target.value))}
              />
            </div>
          </div>
        </div>

        {/* Cài đặt giới hạn */}
        <div style={{ marginBottom: '24px' }}>
          <h4 style={{ margin: '0 0 12px 0', color: '#374151' }}>Cài đặt giới hạn</h4>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
            <div className="form-group">
              <label className="form-label">Follow tối đa/ngày</label>
              <input
                type="number"
                className="form-input"
                min="1"
                max="200"
                value={localSettings.maxFollowsPerDay}
                onChange={(e) => handleSettingChange('maxFollowsPerDay', parseInt(e.target.value))}
              />
            </div>

            <div className="form-group">
              <label className="form-label">Follow tối đa/phiên</label>
              <input
                type="number"
                className="form-input"
                min="1"
                max="50"
                value={localSettings.maxFollowsPerSession}
                onChange={(e) => handleSettingChange('maxFollowsPerSession', parseInt(e.target.value))}
              />
            </div>
          </div>

          <div className="form-group">
            <label className="form-label">Thời gian nghỉ giữa phiên (giây)</label>
            <input
              type="number"
              className="form-input"
              min="300"
              max="7200"
              value={localSettings.restBetweenSessions}
              onChange={(e) => handleSettingChange('restBetweenSessions', parseInt(e.target.value))}
            />
            <small style={{ color: '#6b7280', fontSize: '12px' }}>
              Khuyến nghị: 3600 giây (1 giờ)
            </small>
          </div>
        </div>

        {/* Nút lưu cài đặt */}
        <div>
          <button
            className="btn btn-primary"
            onClick={handleSaveSettings}
            disabled={!isConnected}
            style={{ width: '100%' }}
          >
            💾 Lưu cài đặt
          </button>
        </div>
      </div>
    </div>
  )
}

export default SettingsPanel
