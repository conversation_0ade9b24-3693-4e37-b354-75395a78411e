import React, { useEffect, useRef, useState } from 'react'

function LogViewer({ logs, onClearLogs }) {
  const logContainerRef = useRef(null);
  const [autoScroll, setAutoScroll] = useState(true);
  const [filter, setFilter] = useState('all');

  // Auto scroll to bottom when new logs arrive
  useEffect(() => {
    if (autoScroll && logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logs, autoScroll]);

  const handleScroll = () => {
    if (logContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = logContainerRef.current;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
      setAutoScroll(isAtBottom);
    }
  };

  const getLogIcon = (level) => {
    switch (level) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'info': return 'ℹ️';
      default: return '📝';
    }
  };

  const getLogColor = (level) => {
    switch (level) {
      case 'success': return '#10b981';
      case 'error': return '#ef4444';
      case 'warning': return '#f59e0b';
      case 'info': return '#3b82f6';
      default: return '#6b7280';
    }
  };

  const filteredLogs = logs.filter(log => {
    if (filter === 'all') return true;
    return log.level === filter;
  });

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('vi-VN');
  };

  return (
    <div className="component-card" style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
      <div className="component-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>Log Hệ thống ({filteredLogs.length})</span>
          <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              style={{
                padding: '4px 8px',
                border: '1px solid #d1d5db',
                borderRadius: '4px',
                fontSize: '12px'
              }}
            >
              <option value="all">Tất cả</option>
              <option value="success">Thành công</option>
              <option value="error">Lỗi</option>
              <option value="warning">Cảnh báo</option>
              <option value="info">Thông tin</option>
            </select>

            <button
              className="btn btn-secondary btn-sm"
              onClick={onClearLogs}
              title="Xóa tất cả log"
            >
              🗑️
            </button>

            <button
              className="btn btn-secondary btn-sm"
              onClick={() => setAutoScroll(!autoScroll)}
              title={autoScroll ? "Tắt tự động cuộn" : "Bật tự động cuộn"}
              style={{
                background: autoScroll ? '#10b981' : '#6b7280',
                color: 'white'
              }}
            >
              {autoScroll ? '📌' : '📌'}
            </button>
          </div>
        </div>
      </div>

      <div
        className="component-content"
        style={{
          flex: 1,
          padding: 0,
          display: 'flex',
          flexDirection: 'column',
          minHeight: '300px'
        }}
      >
        {filteredLogs.length === 0 ? (
          <div style={{
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#6b7280',
            fontSize: '14px'
          }}>
            {logs.length === 0 ? 'Chưa có log nào' : 'Không có log phù hợp với bộ lọc'}
          </div>
        ) : (
          <div
            ref={logContainerRef}
            onScroll={handleScroll}
            style={{
              flex: 1,
              overflowY: 'auto',
              padding: '12px',
              fontSize: '13px',
              fontFamily: 'Monaco, Consolas, "Courier New", monospace',
              lineHeight: '1.4',
              background: '#f8fafc'
            }}
          >
            {filteredLogs.map((log) => (
              <div
                key={log.id}
                style={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  gap: '8px',
                  marginBottom: '8px',
                  padding: '8px',
                  background: 'white',
                  borderRadius: '6px',
                  border: `1px solid ${getLogColor(log.level)}20`,
                  borderLeft: `3px solid ${getLogColor(log.level)}`
                }}
              >
                <span style={{ fontSize: '14px' }}>
                  {getLogIcon(log.level)}
                </span>

                <div style={{ flex: 1, minWidth: 0 }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    marginBottom: '4px'
                  }}>
                    <span style={{
                      color: '#6b7280',
                      fontSize: '11px',
                      fontWeight: '500'
                    }}>
                      {formatTime(log.timestamp)}
                    </span>

                    {log.accountId && (
                      <span style={{
                        background: '#e5e7eb',
                        color: '#374151',
                        padding: '2px 6px',
                        borderRadius: '10px',
                        fontSize: '10px',
                        fontWeight: '500'
                      }}>
                        {log.accountId.slice(0, 8)}...
                      </span>
                    )}

                    <span style={{
                      color: getLogColor(log.level),
                      fontSize: '10px',
                      fontWeight: '600',
                      textTransform: 'uppercase'
                    }}>
                      {log.level}
                    </span>
                  </div>

                  <div style={{
                    color: '#374151',
                    wordBreak: 'break-word'
                  }}>
                    {log.message}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {!autoScroll && (
          <div style={{
            position: 'absolute',
            bottom: '20px',
            right: '20px',
            background: '#3b82f6',
            color: 'white',
            padding: '8px 12px',
            borderRadius: '20px',
            fontSize: '12px',
            cursor: 'pointer',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
          }}
          onClick={() => {
            setAutoScroll(true);
            if (logContainerRef.current) {
              logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
            }
          }}>
            ⬇️ Cuộn xuống cuối
          </div>
        )}
      </div>
    </div>
  )
}

export default LogViewer
