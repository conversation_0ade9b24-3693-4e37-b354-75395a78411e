# Format: ip:port:country:city or ip:port:user:pass:country:city
# Example:
# ***********:8080:US:New York
# ***********:8080:username:password:US:Los Angeles
# ***********:8080:::GB:London (no auth, empty user:pass)

***********00:8080:::US:New York
***********01:8080:::US:Los Angeles
***********02:8080:proxyuser:proxypass:US:Chicago
********:3128:::GB:London
********:3128:admin:secret:DE:Berlin
***********:8080:::CA:Toronto
***********:8080:user:pass:AU:Sydney
***********:3128:::FR:Paris
***********:8080:::JP:Tokyo
***********:8080:proxy:secret:KR:Seoul
