{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iCAAoE;AACpE,2CAAqD;AACrD,kDAAgC;AAChC,yCAA2B;AAC3B,yCAA2B;AAC3B,yCAA2B;AAE3B,6BAA0B;AAE1B,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,mBAAmB,CAAC,CAAC;AAE/C,MAAM,0BAA0B,GAAG,CAGlC,OAAU,EACT,EAAE;IACH,IACC,OAAO,CAAC,UAAU,KAAK,SAAS;QAChC,OAAO,CAAC,IAAI;QACZ,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EACtB;QACD,OAAO;YACN,GAAG,OAAO;YACV,UAAU,EAAE,OAAO,CAAC,IAAI;SACxB,CAAC;KACF;IACD,OAAO,OAAO,CAAC;AAChB,CAAC,CAAC;AAEF,SAAS,aAAa,CAAC,GAAQ;IAC9B,IAAI,MAAM,GAAG,KAAK,CAAC;IACnB,IAAI,IAAI,GAAuB,CAAC,CAAC;IACjC,MAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC;IAE1B,0EAA0E;IAC1E,iEAAiE;IACjE,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC;IAE5C,sEAAsE;IACtE,iBAAiB;IACjB,QAAQ,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;QACtC,KAAK,QAAQ;YACZ,MAAM,GAAG,IAAI,CAAC;YACd,IAAI,GAAG,CAAC,CAAC;YACT,MAAM;QACP,eAAe;QACf,KAAK,SAAS;YACb,IAAI,GAAG,CAAC,CAAC;YACT,MAAM;QACP,KAAK,QAAQ;YACZ,MAAM,GAAG,IAAI,CAAC;YACd,IAAI,GAAG,CAAC,CAAC;YACT,MAAM;QACP,eAAe;QACf,KAAK,OAAO,EAAE,sCAAsC;YACnD,IAAI,GAAG,CAAC,CAAC;YACT,MAAM;QACP,KAAK,SAAS;YACb,IAAI,GAAG,CAAC,CAAC;YACT,MAAM;QACP;YACC,MAAM,IAAI,SAAS,CAClB,8CAA8C,MAAM,CACnD,GAAG,CAAC,QAAQ,CACZ,EAAE,CACH,CAAC;KACH;IAED,MAAM,KAAK,GAAe;QACzB,IAAI;QACJ,IAAI;QACJ,IAAI;KACJ,CAAC;IAEF,IAAI,GAAG,CAAC,QAAQ,EAAE;QACjB,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE;YACtC,KAAK,EAAE,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC;YACvC,UAAU,EAAE,KAAK;SACjB,CAAC,CAAC;KACH;IAED,IAAI,GAAG,CAAC,QAAQ,IAAI,IAAI,EAAE;QACzB,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,UAAU,EAAE;YACxC,KAAK,EAAE,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC;YACvC,UAAU,EAAE,KAAK;SACjB,CAAC,CAAC;KACH;IAED,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AAC1B,CAAC;AAYD,MAAa,eAAgB,SAAQ,kBAAK;IAczC,YAAY,GAAiB,EAAE,IAA6B;QAC3D,KAAK,CAAC,IAAI,CAAC,CAAC;QAEZ,MAAM,GAAG,GAAG,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,SAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QACzD,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,IAAI,EAAE,OAAO,IAAI,IAAI,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,IAAI,EAAE,aAAa,IAAI,IAAI,CAAC;IAClD,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,OAAO,CACZ,GAAuB,EACvB,IAAsB;QAEtB,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;QAE9C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;SACtC;QAED,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,GAAG,GAAG,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;QAErD,IAAI,YAAY,EAAE;YACjB,mEAAmE;YACnE,IAAI,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACpD,0DAA0D;gBAC1D,QAAQ,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;oBAC/B,IAAI,GAAG,EAAE;wBACR,MAAM,CAAC,GAAG,CAAC,CAAC;qBACZ;yBAAM;wBACN,OAAO,CAAC,GAAG,CAAC,CAAC;qBACb;gBACF,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC;SACH;QAED,MAAM,SAAS,GAAuB;YACrC,KAAK;YACL,WAAW,EAAE;gBACZ,IAAI;gBACJ,IAAI,EAAE,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC;aAC1D;YACD,OAAO,EAAE,SAAS;YAClB,OAAO,EAAE,OAAO,IAAI,SAAS;YAC7B,0EAA0E;YAC1E,oEAAoE;YACpE,cAAc,EAAE,IAAI,CAAC,aAAa,IAAI,SAAS;SAC/C,CAAC;QAEF,MAAM,OAAO,GAAG,CAAC,SAAyB,EAAE,EAAE;YAC7C,GAAG,CAAC,OAAO,EAAE,CAAC;YACd,MAAM,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,SAAS;gBAAE,SAAS,CAAC,OAAO,EAAE,CAAC;QACpC,CAAC,CAAC;QAEF,KAAK,CAAC,qCAAqC,EAAE,SAAS,CAAC,CAAC;QACxD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,mBAAW,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QACjE,KAAK,CAAC,6CAA6C,CAAC,CAAC;QAErD,IAAI,OAAO,KAAK,IAAI,EAAE;YACrB,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAC3B,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;SACtC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE;YACxB,sDAAsD;YACtD,8CAA8C;YAC9C,KAAK,CAAC,oCAAoC,CAAC,CAAC;YAC5C,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC;gBAC7B,GAAG,IAAI,CACN,0BAA0B,CAAC,IAAI,CAAC,EAChC,MAAM,EACN,MAAM,EACN,MAAM,CACN;gBACD,MAAM;aACN,CAAC,CAAC;YAEH,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBACjC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;gBACzC,OAAO,CAAC,SAAS,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC;SACjB;QAED,OAAO,MAAM,CAAC;IACf,CAAC;;AA3GM,yBAAS,GAAG;IAClB,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,SAAS;CACA,CAAC;AAPC,0CAAe;AA+G5B,SAAS,IAAI,CACZ,GAAM,EACN,GAAG,IAAO;IAIV,MAAM,GAAG,GAAG,EAAkD,CAAC;IAC/D,IAAI,GAAqB,CAAC;IAC1B,KAAK,GAAG,IAAI,GAAG,EAAE;QAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACxB,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;SACpB;KACD;IACD,OAAO,GAAG,CAAC;AACZ,CAAC"}