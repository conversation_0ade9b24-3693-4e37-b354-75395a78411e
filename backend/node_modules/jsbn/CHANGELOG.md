# v1.1.0

- Allow for es6 "default import", e.g. `import BigInteger from 'jsbn'`.
- Updated license file to read MIT


# v1.0.0

- breaking change: `require('jsbn')` no longer returns `BigInteger`. Use `require('jsbn').BigInteger` instead.



# v0.1.1

- fixed backwards-incompatible change in v0.1.0 where `require('jsbn') != BigInteger`. This patch version allows for `var BigInteger = require('jsbn')` or `var BigInteger = require('jsbn').BigInteger`.

