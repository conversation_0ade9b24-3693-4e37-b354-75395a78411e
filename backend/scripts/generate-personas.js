#!/usr/bin/env node

/**
 * Script để tạo ngân hàng personas
 * Chạy: node scripts/generate-personas.js
 */

const path = require('path');
const PersonaGenerator = require('../src/antidetect/persona-generator');

async function main() {
  console.log('🚀 Starting persona generation...');
  
  const generator = new PersonaGenerator();
  
  // Tạo 150 personas với các regions khác nhau
  const regions = ['US', 'CA', 'GB', 'DE', 'FR', 'IT', 'ES', 'AU', 'JP', 'KR'];
  const personas = generator.generatePersonaBank(150, regions);
  
  console.log(`✅ Generated ${personas.length} personas`);
  
  // Thống kê
  const stats = {
    platforms: {},
    regions: {},
    browsers: {}
  };
  
  personas.forEach(persona => {
    stats.platforms[persona.platform] = (stats.platforms[persona.platform] || 0) + 1;
    stats.regions[persona.region] = (stats.regions[persona.region] || 0) + 1;
    stats.browsers[persona.browser.name] = (stats.browsers[persona.browser.name] || 0) + 1;
  });
  
  console.log('\n📊 Statistics:');
  console.log('Platforms:', stats.platforms);
  console.log('Regions:', stats.regions);
  console.log('Browsers:', stats.browsers);
  
  // Lưu vào file
  const outputPath = path.join(__dirname, '../data/personas.json');
  await generator.savePersonasToFile(outputPath);
  
  console.log(`\n💾 Personas saved to: ${outputPath}`);
  console.log('✨ Done!');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
