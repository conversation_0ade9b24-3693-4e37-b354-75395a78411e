const WebSocket = require('ws');
const { formatTime } = require('../utils');

class WebSocketServer {
  constructor(port = 8080) {
    this.port = port;
    this.wss = null;
    this.clients = new Set();
    this.eventHandlers = new Map();
  }

  /**
   * Khởi tạo WebSocket server
   */
  start() {
    this.wss = new WebSocket.Server({ port: this.port });

    this.wss.on('connection', (ws) => {
      console.log(`[${formatTime()}] New client connected`);
      this.clients.add(ws);

      // G<PERSON>i thông báo kết nối thành công
      this.sendToClient(ws, {
        type: 'connection',
        status: 'connected',
        message: 'Connected to backend server',
        timestamp: new Date().toISOString()
      });

      // Xử lý tin nhắn từ client
      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleClientMessage(ws, message);
        } catch (error) {
          console.error('Error parsing client message:', error);
          this.sendError(ws, 'Invalid message format');
        }
      });

      // Xử lý khi client ngắt kết nối
      ws.on('close', () => {
        console.log(`[${formatTime()}] Client disconnected`);
        this.clients.delete(ws);
      });

      // Xử lý lỗi
      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        this.clients.delete(ws);
      });
    });

    console.log(`[${formatTime()}] WebSocket server started on port ${this.port}`);
  }

  /**
   * Xử lý tin nhắn từ client
   */
  async handleClientMessage(ws, message) {
    const { type, command, data } = message;

    console.log(`[${formatTime()}] Received command: ${command || type}`);

    try {
      // Gọi handler tương ứng
      if (this.eventHandlers.has(command || type)) {
        const handler = this.eventHandlers.get(command || type);
        await handler(ws, data);
      } else {
        this.sendError(ws, `Unknown command: ${command || type}`);
      }
    } catch (error) {
      console.error(`Error handling command ${command || type}:`, error);
      this.sendError(ws, `Error executing command: ${error.message}`);
    }
  }

  /**
   * Đăng ký handler cho command
   */
  onCommand(command, handler) {
    this.eventHandlers.set(command, handler);
  }

  /**
   * Gửi tin nhắn đến một client cụ thể
   */
  sendToClient(ws, message) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({
        ...message,
        timestamp: new Date().toISOString()
      }));
    }
  }

  /**
   * Gửi tin nhắn đến tất cả clients
   */
  broadcast(message) {
    const messageWithTimestamp = {
      ...message,
      timestamp: new Date().toISOString()
    };

    this.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(messageWithTimestamp));
      }
    });
  }

  /**
   * Gửi log đến tất cả clients
   */
  sendLog(level, message, accountId = null) {
    this.broadcast({
      type: 'log',
      level,
      message,
      accountId,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Gửi cập nhật trạng thái account
   */
  sendAccountStatusUpdate(accountId, status, additionalData = {}) {
    this.broadcast({
      type: 'account_status_update',
      accountId,
      status,
      ...additionalData
    });
  }

  /**
   * Gửi cập nhật stats
   */
  sendStatsUpdate(stats) {
    this.broadcast({
      type: 'stats_update',
      stats
    });
  }

  /**
   * Gửi lỗi đến client
   */
  sendError(ws, errorMessage) {
    this.sendToClient(ws, {
      type: 'error',
      message: errorMessage
    });
  }

  /**
   * Gửi phản hồi thành công
   */
  sendSuccess(ws, message, data = null) {
    this.sendToClient(ws, {
      type: 'success',
      message,
      data
    });
  }

  /**
   * Dừng server
   */
  stop() {
    if (this.wss) {
      this.wss.close();
      console.log(`[${formatTime()}] WebSocket server stopped`);
    }
  }

  /**
   * Lấy số lượng clients đang kết nối
   */
  getClientCount() {
    return this.clients.size;
  }
}

module.exports = WebSocketServer;
