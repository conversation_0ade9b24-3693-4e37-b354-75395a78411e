const path = require('path');
const DatabaseManager = require('./database/manager');
const WebSocketServer = require('./server/websocket');
const TikTokLoginAutomation = require('./automation/login');
const FollowInteractAutomation = require('./automation/follow-interact');
const CommandRouter = require('./routes/CommandRouter');
const { formatTime } = require('./utils');

class TikTokAutomationBackend {
  constructor() {
    this.db = new DatabaseManager();
    this.wsServer = new WebSocketServer(8080);
    this.loginAutomation = null;
    this.followInteractAutomation = null;
    this.commandRouter = null;
    this.isRunning = false;
  }

  /**
   * Khởi tạo backend
   */
  async initialize() {
    try {
      console.log(`[${formatTime()}] TikTok Automation Backend starting...`);

      // Khởi tạo database
      await this.db.initialize();
      console.log(`[${formatTime()}] Database initialized`);

      // Khởi tạo WebSocket server
      this.wsServer.start();
      console.log(`[${formatTime()}] WebSocket server started on port 8080`);

      // Khởi tạo automation modules
      this.loginAutomation = new TikTokLoginAutomation(this.wsServer, this.db);
      this.followInteractAutomation = new FollowInteractAutomation(this.wsServer, this.db);

      // Khởi tạo command router
      this.commandRouter = new CommandRouter(
        this.db, 
        this.wsServer, 
        this.loginAutomation, 
        this.followInteractAutomation
      );

      // Đăng ký các command handlers
      this.commandRouter.registerCommands();

      this.isRunning = true;
      console.log(`[${formatTime()}] Backend started successfully`);

    } catch (error) {
      console.error(`[${formatTime()}] Failed to start backend:`, error);
      process.exit(1);
    }
  }

  /**
   * Dừng backend
   */
  async shutdown() {
    console.log(`[${formatTime()}] Shutting down backend...`);

    // Đóng tất cả browsers và stop automation
    if (this.followInteractAutomation) {
      try {
        await this.followInteractAutomation.stopAutomation();
      } catch (error) {
        console.error('Error stopping automation:', error);
      }
    }

    if (this.loginAutomation && this.loginAutomation.browsers) {
      for (const [accountId, browser] of this.loginAutomation.browsers) {
        try {
          await browser.close();
          console.log(`[${formatTime()}] Closed browser for account ${accountId}`);
        } catch (error) {
          console.error(`Error closing browser for account ${accountId}:`, error);
        }
      }
    }

    // Đóng WebSocket server
    if (this.wsServer) {
      this.wsServer.stop();
    }

    this.isRunning = false;
    console.log(`[${formatTime()}] Backend shutdown complete`);
  }
}

// Khởi tạo và chạy backend
const backend = new TikTokAutomationBackend();

// Xử lý graceful shutdown
process.on('SIGINT', async () => {
  console.log(`\n[${formatTime()}] Received SIGINT, shutting down gracefully...`);
  await backend.shutdown();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log(`\n[${formatTime()}] Received SIGTERM, shutting down gracefully...`);
  await backend.shutdown();
  process.exit(0);
});

// Xử lý uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error(`[${formatTime()}] Uncaught Exception:`, error);
  backend.shutdown().then(() => process.exit(1));
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(`[${formatTime()}] Unhandled Rejection at:`, promise, 'reason:', reason);
  backend.shutdown().then(() => process.exit(1));
});

// Khởi tạo backend
backend.initialize().catch(error => {
  console.error(`[${formatTime()}] Failed to initialize backend:`, error);
  process.exit(1);
});

module.exports = TikTokAutomationBackend;
