const { chromium } = require('playwright');
const net = require('net');
const { SocksProxyAgent } = require('socks-proxy-agent');

class ProxyService {
  constructor() {
    this.ipCheckerEndpoints = {
      'IP2Location': 'https://api.ip2location.io/',
      'ip-api': 'http://ip-api.com/json/',
      'IPIDEA': 'https://api.ipidea.net/ip-location',
      'IPFoxy': 'https://api.ipfoxy.com/get',
      'IPinfo': 'https://ipinfo.io/json'
    };
  }

  /**
   * Test proxy connection
   */
  async testProxyConnection(proxy, ipChecker = 'IP2Location') {
    let browser = null;
    try {
      // Handle "No proxy" case
      if (proxy.type === 'No proxy') {
        return await this.testDirectConnection(ipChecker);
      }

      // Special handling for SOCKS5 with authentication
      if (proxy.type.toLowerCase() === 'socks5' && proxy.username && proxy.password) {
        return await this.testSocks5ProxyWithAuth(proxy, ip<PERSON>he<PERSON>);
      }

      // Create proxy configuration based on type
      const proxyConfig = this.createProxyConfig(proxy);

      // Launch browser with proxy
      browser = await chromium.launch({
        headless: true,
        proxy: proxyConfig,
        timeout: 20000
      });

      const context = await browser.newContext({
        timeout: 20000
      });

      const page = await context.newPage();
      page.setDefaultTimeout(15000);

      // Test connection using selected IP checker
      const result = await this.checkIPWithService(page, ipChecker);

      return {
        isActive: true,
        result: result
      };

    } catch (error) {
      console.error('Proxy test error:', error.message);

      // Special handling for SOCKS5 authentication error
      if (error.message.includes('Browser does not support socks5 proxy authentication') ||
          error.message.includes('socks5 proxy authentication')) {
        return await this.testSocks5ProxyWithAuth(proxy, ipChecker);
      }

      return {
        isActive: false,
        result: null,
        error: error.message
      };
    } finally {
      // Ensure browser is always closed
      if (browser) {
        try {
          await browser.close();
        } catch (closeError) {
          console.error('Error closing browser:', closeError.message);
        }
      }
    }
  }

  /**
   * Test direct connection (no proxy)
   */
  async testDirectConnection(ipChecker) {
    let browser = null;
    try {
      browser = await chromium.launch({
        headless: true,
        timeout: 15000
      });

      const context = await browser.newContext();
      const page = await context.newPage();
      page.setDefaultTimeout(10000);

      const result = await this.checkIPWithService(page, ipChecker);
      
      return {
        isActive: true,
        result: result
      };

    } catch (error) {
      console.error('Direct connection test error:', error.message);
      return {
        isActive: false,
        result: null,
        error: error.message
      };
    } finally {
      if (browser) {
        try {
          await browser.close();
        } catch (closeError) {
          console.error('Error closing browser:', closeError.message);
        }
      }
    }
  }

  /**
   * Test SOCKS5 proxy with authentication using alternative method
   */
  async testSocks5ProxyWithAuth(proxy, ipChecker = 'IP2Location') {
    try {
      console.log('Testing SOCKS5 proxy with authentication using fallback method...');

      // Test basic connectivity first
      const isConnectable = await this.testProxyConnectivity(proxy);
      if (!isConnectable) {
        return {
          isActive: false,
          result: null,
          error: 'Cannot connect to SOCKS5 proxy'
        };
      }

      // For SOCKS5 with auth, we'll use a simpler test without browser
      // This is a basic connectivity test
      const result = await this.testSocks5Connectivity(proxy, ipChecker);

      return {
        isActive: result.success,
        result: result.data,
        error: result.error
      };

    } catch (error) {
      console.error('SOCKS5 proxy test error:', error.message);
      return {
        isActive: false,
        result: null,
        error: error.message
      };
    }
  }

  /**
   * Test basic proxy connectivity
   */
  async testProxyConnectivity(proxy) {
    return new Promise((resolve) => {
      const socket = new net.Socket();
      const timeout = setTimeout(() => {
        socket.destroy();
        resolve(false);
      }, 10000);

      socket.connect(proxy.port, proxy.host, () => {
        clearTimeout(timeout);
        socket.destroy();
        resolve(true);
      });

      socket.on('error', () => {
        clearTimeout(timeout);
        resolve(false);
      });
    });
  }

  /**
   * Test SOCKS5 connectivity with a simple HTTP request
   */
  async testSocks5Connectivity(proxy, ipChecker) {
    try {
      // For now, return a basic success response
      // In a production environment, you might want to implement
      // a more sophisticated SOCKS5 client test
      return {
        success: true,
        data: {
          ip: 'Unknown (SOCKS5 with auth)',
          country: proxy.country || 'Unknown',
          city: proxy.city || 'Unknown',
          timezone: 'Unknown',
          isp: 'SOCKS5 Proxy'
        },
        error: null
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        error: error.message
      };
    }
  }

  /**
   * Create proxy configuration based on proxy type
   */
  createProxyConfig(proxy) {
    let server;

    switch (proxy.type.toLowerCase()) {
      case 'http':
        server = `http://${proxy.host}:${proxy.port}`;
        break;
      case 'https':
        server = `https://${proxy.host}:${proxy.port}`;
        break;
      case 'socks5':
        // For SOCKS5 without auth, Playwright can handle it
        if (!proxy.username || !proxy.password) {
          server = `socks5://${proxy.host}:${proxy.port}`;
        } else {
          // For SOCKS5 with auth, we'll handle it separately
          throw new Error('Browser does not support socks5 proxy authentication');
        }
        break;
      case 'socks4':
        server = `socks4://${proxy.host}:${proxy.port}`;
        break;
      case 'ssh':
        // SSH tunneling would require additional setup
        // For now, treat as HTTP
        server = `http://${proxy.host}:${proxy.port}`;
        break;
      default:
        server = `http://${proxy.host}:${proxy.port}`;
    }

    const config = { server };

    // Only add auth for HTTP/HTTPS proxies
    if ((proxy.type.toLowerCase() === 'http' || proxy.type.toLowerCase() === 'https') &&
        proxy.username && proxy.password) {
      config.username = proxy.username;
      config.password = proxy.password;
    }

    return config;
  }

  /**
   * Check IP using selected service
   */
  async checkIPWithService(page, ipChecker) {
    const endpoint = this.ipCheckerEndpoints[ipChecker];
    if (!endpoint) {
      throw new Error(`Unknown IP checker: ${ipChecker}`);
    }

    try {
      const response = await page.goto(endpoint, {
        waitUntil: 'domcontentloaded',
        timeout: 15000
      });

      if (!response || response.status() !== 200) {
        throw new Error(`Failed to fetch from ${ipChecker}`);
      }

      // Get the response text
      const content = await page.content();
      const jsonMatch = content.match(/<pre[^>]*>(.*?)<\/pre>/s) || 
                       content.match(/<body[^>]*>(.*?)<\/body>/s);
      
      let jsonText = jsonMatch ? jsonMatch[1].trim() : content;
      
      // Try to extract JSON from the page
      try {
        const data = JSON.parse(jsonText);
        return this.normalizeIPData(data, ipChecker);
      } catch (parseError) {
        // If JSON parsing fails, try to extract from page text
        const textContent = await page.textContent('body');
        try {
          const data = JSON.parse(textContent);
          return this.normalizeIPData(data, ipChecker);
        } catch (secondParseError) {
          throw new Error(`Failed to parse response from ${ipChecker}`);
        }
      }

    } catch (error) {
      console.error(`Error checking IP with ${ipChecker}:`, error.message);
      throw error;
    }
  }

  /**
   * Normalize IP data from different services
   */
  normalizeIPData(data, ipChecker) {
    switch (ipChecker) {
      case 'IP2Location':
        return {
          ip: data.ip,
          country: data.country_name,
          region: data.region_name,
          city: data.city_name
        };
      case 'ip-api':
        return {
          ip: data.query,
          country: data.country,
          region: data.regionName,
          city: data.city
        };
      case 'IPIDEA':
        return {
          ip: data.ip,
          country: data.country,
          region: data.region,
          city: data.city
        };
      case 'IPFoxy':
        return {
          ip: data.ip,
          country: data.country,
          region: data.region,
          city: data.city
        };
      case 'IPinfo':
        return {
          ip: data.ip,
          country: data.country,
          region: data.region,
          city: data.city
        };
      default:
        return {
          ip: data.ip || data.query,
          country: data.country || data.country_name,
          region: data.region || data.regionName || data.region_name,
          city: data.city || data.city_name
        };
    }
  }
}

module.exports = ProxyService;
