const {
  readAccountsFile,
  readProxiesFile,
  readCommentsFile
} = require('../utils');
const path = require('path');

class SettingsService {
  constructor(dbManager, wsServer) {
    this.dbManager = dbManager;
    this.wsServer = wsServer;
  }

  /**
   * Get settings
   */
  async getSettings(ws, data) {
    try {
      const settings = await this.dbManager.getSettings();
      this.wsServer.sendSuccess(ws, 'Settings retrieved successfully', { settings });
    } catch (error) {
      this.wsServer.sendError(ws, `Failed to get settings: ${error.message}`);
    }
  }

  /**
   * Update settings
   */
  async updateSettings(ws, data) {
    try {
      const { settings } = data;
      if (!settings) {
        this.wsServer.sendError(ws, 'Settings data is required');
        return;
      }

      await this.dbManager.updateSettings(settings);
      this.wsServer.sendSuccess(ws, 'Settings updated successfully');
      this.wsServer.sendLog('info', 'Settings updated');
    } catch (error) {
      this.wsServer.sendError(ws, `Failed to update settings: ${error.message}`);
    }
  }

  /**
   * Load accounts from file
   */
  async loadAccounts(ws, data) {
    try {
      const filePath = data.filePath || path.join(__dirname, '../../data/accounts.txt');
      const accounts = await readAccountsFile(filePath);

      if (accounts.length > 0) {
        await this.dbManager.addAccounts(accounts);
        this.wsServer.sendSuccess(ws, `Loaded ${accounts.length} accounts successfully`);
        this.wsServer.sendLog('info', `Loaded ${accounts.length} accounts from file`);
      } else {
        this.wsServer.sendError(ws, 'No valid accounts found in file');
      }
    } catch (error) {
      this.wsServer.sendError(ws, `Failed to load accounts: ${error.message}`);
    }
  }

  /**
   * Load proxies from file
   */
  async loadProxies(ws, data) {
    try {
      const filePath = data.filePath || path.join(__dirname, '../../data/proxies.txt');
      const proxies = await readProxiesFile(filePath);

      if (proxies.length > 0) {
        await this.dbManager.addProxies(proxies);
        this.wsServer.sendSuccess(ws, `Loaded ${proxies.length} proxies successfully`);
        this.wsServer.sendLog('info', `Loaded ${proxies.length} proxies from file`);
      } else {
        this.wsServer.sendError(ws, 'No valid proxies found in file');
      }
    } catch (error) {
      this.wsServer.sendError(ws, `Failed to load proxies: ${error.message}`);
    }
  }

  /**
   * Load comments from file
   */
  async loadComments(ws, data) {
    try {
      const filePath = data.filePath || path.join(__dirname, '../../data/comments.txt');
      const comments = await readCommentsFile(filePath);

      if (comments.length > 0) {
        await this.dbManager.addComments(comments);
        this.wsServer.sendSuccess(ws, `Loaded ${comments.length} comments successfully`);
        this.wsServer.sendLog('info', `Loaded ${comments.length} comments from file`);
      } else {
        this.wsServer.sendError(ws, 'No valid comments found in file');
      }
    } catch (error) {
      this.wsServer.sendError(ws, `Failed to load comments: ${error.message}`);
    }
  }
}

module.exports = SettingsService;
