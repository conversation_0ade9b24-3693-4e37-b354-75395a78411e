/**
 * Persona Structure Definition
 * Định nghĩa cấu trúc "Persona" - một đối tượng chứa bộ thông số vân tay hoàn chỉnh
 * và thống nhất cho một loại thiết bị cụ thể
 */

/**
 * @typedef {Object} PersonaScreen
 * @property {number} width - Chiều rộng màn hình
 * @property {number} height - Chiều cao màn hình
 * @property {number} availWidth - Chiều rộng khả dụng
 * @property {number} availHeight - Chiều cao khả dụng
 * @property {number} colorDepth - <PERSON><PERSON> sâu màu (thường là 24)
 * @property {number} pixelDepth - <PERSON><PERSON> sâu pixel (thường là 24)
 */

/**
 * @typedef {Object} PersonaWebGL
 * @property {string} vendor - Nhà cung cấp WebGL (ví dụ: "Google Inc. (NVIDIA)")
 * @property {string} renderer - Renderer WebGL (ví dụ: "ANGLE (NVIDIA, ...)")
 * @property {string} version - <PERSON>ên bản WebGL
 * @property {string} shadingLanguageVersion - Phiên bản shading language
 */

/**
 * @typedef {Object} PersonaCanvas
 * @property {number} noiseLevel - Mức độ nhiễu cho Canvas fingerprinting (0-1)
 * @property {string} toDataURLNoise - Chuỗi nhiễu cho toDataURL
 */

/**
 * @typedef {Object} PersonaGeolocation
 * @property {number} latitude - Vĩ độ
 * @property {number} longitude - Kinh độ
 * @property {number} accuracy - Độ chính xác (meters)
 */

/**
 * @typedef {Object} Persona
 * @property {string} id - ID duy nhất của persona
 * @property {string} platform - Hệ điều hành ("Windows", "macOS", "Linux")
 * @property {string} platformVersion - Phiên bản hệ điều hành
 * @property {string} userAgent - Chuỗi User-Agent hoàn chỉnh
 * @property {PersonaScreen} screen - Thông tin màn hình
 * @property {PersonaWebGL} webgl - Thông tin WebGL
 * @property {PersonaCanvas} canvas - Cấu hình Canvas fingerprinting
 * @property {string[]} fonts - Danh sách fonts có sẵn
 * @property {string} language - Ngôn ngữ chính (ví dụ: "en-US")
 * @property {string[]} languages - Danh sách ngôn ngữ ưu tiên
 * @property {string} timezone - Múi giờ (ví dụ: "America/New_York")
 * @property {PersonaGeolocation} geolocation - Vị trí địa lý mặc định
 * @property {Object} hardware - Thông tin phần cứng
 * @property {number} hardware.deviceMemory - Bộ nhớ thiết bị (GB)
 * @property {number} hardware.hardwareConcurrency - Số lõi CPU
 * @property {Object} browser - Thông tin trình duyệt
 * @property {string} browser.name - Tên trình duyệt
 * @property {string} browser.version - Phiên bản trình duyệt
 * @property {Object} network - Thông tin mạng
 * @property {string} network.effectiveType - Loại kết nối mạng
 * @property {number} network.downlink - Tốc độ download (Mbps)
 * @property {number} network.rtt - Round trip time (ms)
 */

class PersonaManager {
  constructor() {
    this.personas = [];
    this.loadedPersonas = false;
  }

  /**
   * Tạo một persona mẫu cho Windows 10 - Chrome
   * @returns {Persona}
   */
  createSampleWindowsPersona() {
    return {
      id: 'windows-chrome-sample-1',
      platform: 'Windows',
      platformVersion: '10.0.0',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      screen: {
        width: 1920,
        height: 1080,
        availWidth: 1920,
        availHeight: 1040,
        colorDepth: 24,
        pixelDepth: 24
      },
      webgl: {
        vendor: 'Google Inc. (NVIDIA)',
        renderer: 'ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11)',
        version: 'WebGL 1.0 (OpenGL ES 2.0 Chromium)',
        shadingLanguageVersion: 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)'
      },
      canvas: {
        noiseLevel: 0.1,
        toDataURLNoise: 'windows-chrome-noise-1'
      },
      fonts: [
        'Arial', 'Arial Black', 'Calibri', 'Cambria', 'Comic Sans MS',
        'Consolas', 'Courier New', 'Georgia', 'Impact', 'Lucida Console',
        'Lucida Sans Unicode', 'Microsoft Sans Serif', 'Palatino Linotype',
        'Segoe UI', 'Tahoma', 'Times New Roman', 'Trebuchet MS', 'Verdana'
      ],
      language: 'en-US',
      languages: ['en-US', 'en'],
      timezone: 'America/New_York',
      geolocation: {
        latitude: 40.7128,
        longitude: -74.0060,
        accuracy: 100
      },
      hardware: {
        deviceMemory: 8,
        hardwareConcurrency: 8
      },
      browser: {
        name: 'Chrome',
        version: '120.0.0.0'
      },
      network: {
        effectiveType: '4g',
        downlink: 10,
        rtt: 50
      }
    };
  }

  /**
   * Tạo một persona mẫu cho macOS - Chrome
   * @returns {Persona}
   */
  createSampleMacPersona() {
    return {
      id: 'macos-chrome-sample-1',
      platform: 'macOS',
      platformVersion: '10.15.7',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      screen: {
        width: 2560,
        height: 1440,
        availWidth: 2560,
        availHeight: 1415,
        colorDepth: 24,
        pixelDepth: 24
      },
      webgl: {
        vendor: 'Google Inc. (Apple)',
        renderer: 'ANGLE (Apple, Apple M1, OpenGL 4.1)',
        version: 'WebGL 1.0 (OpenGL ES 2.0 Chromium)',
        shadingLanguageVersion: 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)'
      },
      canvas: {
        noiseLevel: 0.1,
        toDataURLNoise: 'macos-chrome-noise-1'
      },
      fonts: [
        'Arial', 'Arial Black', 'Helvetica', 'Helvetica Neue', 'Times',
        'Times New Roman', 'Courier', 'Courier New', 'Verdana', 'Georgia',
        'Palatino', 'Garamond', 'Bookman', 'Avant Garde', 'San Francisco',
        'Monaco', 'Menlo', 'Lucida Grande'
      ],
      language: 'en-US',
      languages: ['en-US', 'en'],
      timezone: 'America/Los_Angeles',
      geolocation: {
        latitude: 37.7749,
        longitude: -122.4194,
        accuracy: 100
      },
      hardware: {
        deviceMemory: 16,
        hardwareConcurrency: 8
      },
      browser: {
        name: 'Chrome',
        version: '120.0.0.0'
      },
      network: {
        effectiveType: '4g',
        downlink: 15,
        rtt: 30
      }
    };
  }

  /**
   * Validate persona object
   * @param {Persona} persona 
   * @returns {boolean}
   */
  validatePersona(persona) {
    const requiredFields = [
      'id', 'platform', 'userAgent', 'screen', 'webgl', 'fonts',
      'language', 'timezone', 'hardware', 'browser'
    ];

    for (const field of requiredFields) {
      if (!persona[field]) {
        console.error(`Missing required field: ${field}`);
        return false;
      }
    }

    // Validate screen object
    if (!persona.screen.width || !persona.screen.height) {
      console.error('Invalid screen dimensions');
      return false;
    }

    // Validate fonts array
    if (!Array.isArray(persona.fonts) || persona.fonts.length === 0) {
      console.error('Invalid fonts array');
      return false;
    }

    return true;
  }
}

module.exports = {
  PersonaManager
};
