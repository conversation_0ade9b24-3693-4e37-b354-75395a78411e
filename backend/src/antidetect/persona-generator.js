/**
 * Persona Generator
 * Tạo ra ngân hàng personas từ dữ liệu vân tay đã thu thập
 */

const { generateUUID, randomInt } = require('../utils');
const {
  WINDOWS_USER_AGENTS,
  MACOS_USER_AGENTS,
  SCREEN_RESOLUTIONS,
  NVIDIA_WEBGL_CONFIGS,
  AMD_WEBGL_CONFIGS,
  INTEL_WEBGL_CONFIGS,
  APPLE_WEBGL_CONFIGS,
  WINDOWS_FONTS,
  MACOS_FONTS,
  TIMEZONE_BY_REGION,
  LANGUAGE_BY_REGION
} = require('./fingerprint-data');

class PersonaGenerator {
  constructor() {
    this.personas = [];
  }

  /**
   * Lấy ngẫu nhiên một phần tử từ mảng
   * @param {Array} array 
   * @returns {*}
   */
  getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
  }

  /**
   * <PERSON><PERSON>y ngẫu nhiên nhiều phần tử từ mảng
   * @param {Array} array 
   * @param {number} count 
   * @returns {Array}
   */
  getRandomElements(array, count) {
    const shuffled = [...array].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }

  /**
   * Tạo persona Windows
   * @param {string} region - Mã vùng địa lý (US, GB, DE, etc.)
   * @returns {Object}
   */
  generateWindowsPersona(region = 'US') {
    const userAgent = this.getRandomElement(WINDOWS_USER_AGENTS);
    const screen = this.getRandomElement(SCREEN_RESOLUTIONS);
    const webglConfigs = [...NVIDIA_WEBGL_CONFIGS, ...AMD_WEBGL_CONFIGS, ...INTEL_WEBGL_CONFIGS];
    const webgl = this.getRandomElement(webglConfigs);
    const timezone = this.getRandomElement(TIMEZONE_BY_REGION[region] || TIMEZONE_BY_REGION['US']);
    const language = LANGUAGE_BY_REGION[region] || LANGUAGE_BY_REGION['US'];

    // Tạo availWidth và availHeight (thường nhỏ hơn width/height một chút do taskbar)
    const availHeight = screen.height - randomInt(40, 80);
    const availWidth = screen.width;

    return {
      id: generateUUID(),
      platform: 'Windows',
      platformVersion: randomInt(0, 1) === 0 ? '10.0.0' : '11.0.0',
      userAgent,
      screen: {
        width: screen.width,
        height: screen.height,
        availWidth,
        availHeight,
        colorDepth: 24,
        pixelDepth: 24
      },
      webgl,
      canvas: {
        noiseLevel: Math.random() * 0.2 + 0.05, // 0.05 - 0.25
        toDataURLNoise: `windows-${generateUUID().slice(0, 8)}`
      },
      fonts: this.getRandomElements(WINDOWS_FONTS, randomInt(45, 55)),
      language: language.primary,
      languages: [language.primary, ...language.secondary],
      timezone,
      geolocation: this.generateGeolocation(region),
      hardware: {
        deviceMemory: this.getRandomElement([4, 8, 16, 32]),
        hardwareConcurrency: this.getRandomElement([4, 6, 8, 12, 16])
      },
      browser: {
        name: 'Chrome',
        version: this.extractChromeVersion(userAgent)
      },
      network: {
        effectiveType: this.getRandomElement(['3g', '4g']),
        downlink: randomInt(5, 50),
        rtt: randomInt(20, 100)
      },
      region
    };
  }

  /**
   * Tạo persona macOS
   * @param {string} region - Mã vùng địa lý
   * @returns {Object}
   */
  generateMacPersona(region = 'US') {
    const userAgent = this.getRandomElement(MACOS_USER_AGENTS);
    const screen = this.getRandomElement(SCREEN_RESOLUTIONS.filter(s => s.width >= 1440)); // Mac thường có màn hình lớn hơn
    const webgl = this.getRandomElement(APPLE_WEBGL_CONFIGS);
    const timezone = this.getRandomElement(TIMEZONE_BY_REGION[region] || TIMEZONE_BY_REGION['US']);
    const language = LANGUAGE_BY_REGION[region] || LANGUAGE_BY_REGION['US'];

    const availHeight = screen.height - randomInt(25, 50); // macOS menu bar
    const availWidth = screen.width;

    return {
      id: generateUUID(),
      platform: 'macOS',
      platformVersion: this.getRandomElement(['10.15.7', '11.7.10', '12.7.1', '13.6.3', '14.2.1']),
      userAgent,
      screen: {
        width: screen.width,
        height: screen.height,
        availWidth,
        availHeight,
        colorDepth: 24,
        pixelDepth: 24
      },
      webgl,
      canvas: {
        noiseLevel: Math.random() * 0.2 + 0.05,
        toDataURLNoise: `macos-${generateUUID().slice(0, 8)}`
      },
      fonts: this.getRandomElements(MACOS_FONTS, randomInt(40, 50)),
      language: language.primary,
      languages: [language.primary, ...language.secondary],
      timezone,
      geolocation: this.generateGeolocation(region),
      hardware: {
        deviceMemory: this.getRandomElement([8, 16, 32, 64]),
        hardwareConcurrency: this.getRandomElement([8, 10, 12, 16])
      },
      browser: {
        name: 'Chrome',
        version: this.extractChromeVersion(userAgent)
      },
      network: {
        effectiveType: this.getRandomElement(['4g', '5g']),
        downlink: randomInt(10, 100),
        rtt: randomInt(10, 50)
      },
      region
    };
  }

  /**
   * Tạo geolocation dựa trên region
   * @param {string} region 
   * @returns {Object}
   */
  generateGeolocation(region) {
    const coordinates = {
      'US': { lat: [25, 49], lng: [-125, -66] },
      'CA': { lat: [42, 70], lng: [-141, -52] },
      'GB': { lat: [50, 61], lng: [-8, 2] },
      'DE': { lat: [47, 55], lng: [6, 15] },
      'FR': { lat: [42, 51], lng: [-5, 8] },
      'IT': { lat: [36, 47], lng: [6, 19] },
      'ES': { lat: [36, 44], lng: [-9, 4] },
      'AU': { lat: [-44, -10], lng: [113, 154] },
      'JP': { lat: [24, 46], lng: [123, 146] },
      'KR': { lat: [33, 39], lng: [124, 132] },
      'CN': { lat: [18, 54], lng: [73, 135] },
      'IN': { lat: [6, 37], lng: [68, 97] },
      'BR': { lat: [-34, 5], lng: [-74, -32] },
      'MX': { lat: [14, 33], lng: [-118, -86] }
    };

    const coord = coordinates[region] || coordinates['US'];
    const latitude = Math.random() * (coord.lat[1] - coord.lat[0]) + coord.lat[0];
    const longitude = Math.random() * (coord.lng[1] - coord.lng[0]) + coord.lng[0];

    return {
      latitude: Math.round(latitude * 10000) / 10000,
      longitude: Math.round(longitude * 10000) / 10000,
      accuracy: randomInt(50, 200)
    };
  }

  /**
   * Trích xuất phiên bản Chrome từ User-Agent
   * @param {string} userAgent 
   * @returns {string}
   */
  extractChromeVersion(userAgent) {
    const match = userAgent.match(/Chrome\/(\d+\.\d+\.\d+\.\d+)/);
    return match ? match[1] : '120.0.0.0';
  }

  /**
   * Tạo ngân hàng personas
   * @param {number} count - Số lượng personas cần tạo
   * @param {Array} regions - Danh sách regions
   * @returns {Array}
   */
  generatePersonaBank(count = 100, regions = ['US', 'CA', 'GB', 'DE', 'FR', 'AU']) {
    const personas = [];
    const platforms = ['Windows', 'macOS'];
    const platformWeights = { 'Windows': 0.7, 'macOS': 0.3 }; // 70% Windows, 30% macOS

    for (let i = 0; i < count; i++) {
      const platform = Math.random() < platformWeights.Windows ? 'Windows' : 'macOS';
      const region = this.getRandomElement(regions);

      let persona;
      if (platform === 'Windows') {
        persona = this.generateWindowsPersona(region);
      } else {
        persona = this.generateMacPersona(region);
      }

      personas.push(persona);
    }

    this.personas = personas;
    return personas;
  }

  /**
   * Lưu personas vào file JSON
   * @param {string} filePath 
   * @returns {Promise<void>}
   */
  async savePersonasToFile(filePath) {
    const fs = require('fs').promises;
    const data = {
      generated: new Date().toISOString(),
      count: this.personas.length,
      personas: this.personas
    };

    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8');
    console.log(`Saved ${this.personas.length} personas to ${filePath}`);
  }

  /**
   * Load personas từ file JSON
   * @param {string} filePath 
   * @returns {Promise<Array>}
   */
  async loadPersonasFromFile(filePath) {
    const fs = require('fs').promises;
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const data = JSON.parse(content);
      this.personas = data.personas || [];
      return this.personas;
    } catch (error) {
      console.error('Error loading personas:', error);
      return [];
    }
  }
}

module.exports = PersonaGenerator;
