const fs = require('fs').promises;
const path = require('path');
const { generateUUID } = require('../utils');

class DatabaseManager {
  constructor(dbPath = path.join(__dirname, '../../db.json')) {
    this.dbPath = dbPath;
    this.data = null;
    this.isLoading = false;
  }

  /**
   * Khởi tạo database, tạo file nếu chưa tồn tại
   */
  async initialize() {
    try {
      await this.loadData();
    } catch (error) {
      console.log('Creating new database file...');
      await this.createDefaultDatabase();
    }
  }

  /**
   * Tạo database mặc định
   */
  async createDefaultDatabase() {
    const defaultData = {
      accounts: [],
      proxies: [],
      comments: [],
      settings: {
        targetProfile: "",
        videosToWatch: 3,
        watchTimeSeconds: 30,
        maxFollowsPerDay: 50,
        maxFollowsPerSession: 10,
        restBetweenSessions: 3600
      },
      stats: {}
    };

    await this.saveData(defaultData);
  }

  /**
   * Load dữ liệu từ file
   */
  async loadData() {
    if (this.isLoading) {
      // Đợi nếu đang load
      while (this.isLoading) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }
      return this.data;
    }

    this.isLoading = true;
    try {
      const content = await fs.readFile(this.dbPath, 'utf-8');
      this.data = JSON.parse(content);
      return this.data;
    } catch (error) {
      throw new Error(`Failed to load database: ${error.message}`);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Lưu dữ liệu vào file
   */
  async saveData(data = null) {
    const dataToSave = data || this.data;
    try {
      await fs.writeFile(this.dbPath, JSON.stringify(dataToSave, null, 2), 'utf-8');
      if (!data) {
        this.data = dataToSave;
      }
    } catch (error) {
      throw new Error(`Failed to save database: ${error.message}`);
    }
  }

  /**
   * Lấy tất cả accounts
   */
  async getAccounts() {
    await this.loadData();
    return this.data.accounts || [];
  }

  /**
   * Thêm accounts mới
   */
  async addAccounts(accounts) {
    await this.loadData();
    this.data.accounts = [...(this.data.accounts || []), ...accounts];
    await this.saveData();
    return this.data.accounts;
  }

  /**
   * Thêm một account mới
   */
  async addAccount(account) {
    await this.loadData();
    this.data.accounts = [...(this.data.accounts || []), account];
    await this.saveData();
    return account;
  }

  /**
   * Lấy một account theo ID
   */
  async getAccount(accountId) {
    await this.loadData();
    return this.data.accounts.find(account => account.id === accountId);
  }

  /**
   * Xóa một account theo ID
   */
  async deleteAccount(accountId) {
    await this.loadData();
    const initialLength = this.data.accounts.length;
    this.data.accounts = this.data.accounts.filter(account => account.id !== accountId);

    if (this.data.accounts.length === initialLength) {
      throw new Error('Account not found');
    }

    await this.saveData();
    return true;
  }

  /**
   * Cập nhật account
   */
  async updateAccount(accountId, updates) {
    await this.loadData();
    const accountIndex = this.data.accounts.findIndex(acc => acc.id === accountId);
    if (accountIndex !== -1) {
      this.data.accounts[accountIndex] = { ...this.data.accounts[accountIndex], ...updates };
      await this.saveData();
      return this.data.accounts[accountIndex];
    }
    throw new Error(`Account with ID ${accountId} not found`);
  }

  /**
   * Xóa account
   */
  async deleteAccount(accountId) {
    await this.loadData();
    this.data.accounts = this.data.accounts.filter(acc => acc.id !== accountId);
    await this.saveData();
    return true;
  }

  /**
   * Lấy account theo ID
   */
  async getAccountById(accountId) {
    await this.loadData();
    return this.data.accounts.find(acc => acc.id === accountId);
  }

  /**
   * Lấy tất cả proxies
   */
  async getProxies() {
    await this.loadData();
    return this.data.proxies || [];
  }

  /**
   * Thêm proxies mới
   */
  async addProxies(proxies) {
    await this.loadData();
    this.data.proxies = [...(this.data.proxies || []), ...proxies];
    await this.saveData();
    return this.data.proxies;
  }

  /**
   * Lấy tất cả comments
   */
  async getComments() {
    await this.loadData();
    return this.data.comments || [];
  }

  /**
   * Thêm comments mới
   */
  async addComments(comments) {
    await this.loadData();
    this.data.comments = [...(this.data.comments || []), ...comments];
    await this.saveData();
    return this.data.comments;
  }

  /**
   * Lấy settings
   */
  async getSettings() {
    await this.loadData();
    return this.data.settings || {};
  }

  /**
   * Cập nhật settings
   */
  async updateSettings(newSettings) {
    await this.loadData();
    this.data.settings = { ...this.data.settings, ...newSettings };
    await this.saveData();
    return this.data.settings;
  }

  /**
   * Lấy stats
   */
  async getStats() {
    await this.loadData();
    return this.data.stats || {};
  }

  /**
   * Cập nhật stats
   */
  async updateStats(newStats) {
    await this.loadData();
    this.data.stats = { ...this.data.stats, ...newStats };
    await this.saveData();
    return this.data.stats;
  }

  /**
   * Reset tất cả dữ liệu
   */
  async resetDatabase() {
    await this.createDefaultDatabase();
    return true;
  }

  /**
   * Gán proxy cho account
   */
  async assignProxyToAccount(accountId, proxyId) {
    const account = await this.getAccountById(accountId);
    if (!account) {
      throw new Error(`Account with ID ${accountId} not found`);
    }

    const proxies = await this.getProxies();
    const proxy = proxies.find(p => p.id === proxyId);
    if (!proxy) {
      throw new Error(`Proxy with ID ${proxyId} not found`);
    }

    return await this.updateAccount(accountId, { proxy: proxy });
  }

  /**
   * Lấy proxy khả dụng ngẫu nhiên
   */
  async getRandomAvailableProxy() {
    const proxies = await this.getProxies();
    const availableProxies = proxies.filter(p => p.isActive);

    if (availableProxies.length === 0) {
      return null;
    }

    return availableProxies[Math.floor(Math.random() * availableProxies.length)];
  }
}

module.exports = DatabaseManager;
