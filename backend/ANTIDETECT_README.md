# 🎭 Antidetect Browser System

Hệ thống Antidetect mạnh mẽ được tích hợp vào backend Node.js, đ<PERSON><PERSON> bảo mỗi profile trình duyệt do Playwright tạo ra có một "vân tay" (fingerprint) đ<PERSON><PERSON> nh<PERSON>, hợ<PERSON> lý và khó bị phát hiện.

## 🏗️ Kiến trúc Hệ thống

### 1. Persona System
- **Persona**: Một đối tượng chứa bộ thông số vân tay hoàn chỉnh cho một loại thiết bị cụ thể
- **Ngân hàng Personas**: 150+ personas đa dạng cho Windows/macOS với các regions khác nhau
- **Tái sử dụng**: Mỗi account giữ nguyên persona trong suốt vòng đời

### 2. Fingerprint Components
- **User-Agent**: Chuỗi UA thực tế từ Chrome mới nhất
- **Screen Resolution**: Đ<PERSON> phân giải màn hình phổ biến
- **WebGL**: Vendor/Renderer từ các card đồ họa thực (NVIDIA, AMD, Intel, Apple)
- **Canvas**: Fingerprinting protection với noise injection
- **Fonts**: Danh sách fonts mặc định theo OS
- **Hardware**: deviceMemory, hardwareConcurrency
- **Geolocation**: Vị trí địa lý dựa trên proxy

### 3. Proxy Integration
- **Format mới**: `ip:port:user:pass:country:city`
- **Auto-mapping**: Timezone và locale dựa trên proxy location
- **Geolocation**: Tọa độ địa lý phù hợp với proxy

## 📁 Cấu trúc Files

```
backend/
├── src/antidetect/
│   ├── persona.js              # Định nghĩa cấu trúc Persona
│   ├── fingerprint-data.js     # Dữ liệu vân tay thực tế
│   ├── persona-generator.js    # Generator tạo personas
│   └── antidetect-manager.js   # Manager chính
├── data/
│   ├── personas.json           # Ngân hàng 150 personas
│   └── proxies.txt            # Proxies với geo info
├── scripts/
│   └── generate-personas.js    # Script tạo personas
└── test-antidetect.js         # Test script
```

## 🚀 Cách sử dụng

### 1. Generate Personas (Đã thực hiện)
```bash
node scripts/generate-personas.js
```

### 2. Update Proxy Format
Cập nhật file `data/proxies.txt` với format mới:
```
# Format: ip:port:user:pass:country:city
*************:8080:::US:New York
*************:8080:user:pass:GB:London
```

### 3. Test System
```bash
node test-antidetect.js
```

## 🔧 Tích hợp vào Login System

Hệ thống đã được tích hợp vào `TikTokLoginAutomation`:

1. **Persona Selection**: Tự động chọn persona phù hợp với proxy region
2. **Context Creation**: Áp dụng tất cả thông số vân tay
3. **Script Injection**: Inject spoofing script vào mọi page
4. **Persistence**: Lưu persona vào database, tái sử dụng cho lần sau

## 🎯 Tính năng Antidetect

### WebGL Spoofing
- Ghi đè `getParameter()` để trả về vendor/renderer từ persona
- Hỗ trợ cả WebGL 1.0 và 2.0

### Canvas Protection
- Thêm noise vào `toDataURL()` output
- Mỗi persona có noise pattern riêng

### Screen Fingerprinting
- Override screen properties (width, height, availWidth, availHeight)
- ColorDepth và pixelDepth consistency

### Hardware Spoofing
- deviceMemory và hardwareConcurrency từ persona
- Phù hợp với platform (Windows/macOS)

### Locale & Timezone
- Tự động mapping từ proxy location
- Consistent language preferences

### WebRTC Protection
- Disable WebRTC local IP leakage
- Browser launch args bảo vệ

## 📊 Thống kê Personas

- **Total**: 150 personas
- **Windows**: ~70% (102 personas)
- **macOS**: ~30% (48 personas)
- **Regions**: US, CA, GB, DE, FR, IT, ES, AU, JP, KR
- **Browsers**: Chrome (multiple versions)

## 🔍 Monitoring & Logs

Hệ thống ghi log chi tiết:
- Persona assignment cho account mới
- Persona reuse cho account cũ
- Proxy-region mapping
- Context creation success

## 🛡️ Security Features

1. **Unique Fingerprints**: Mỗi persona có vân tay độc nhất
2. **Realistic Data**: Tất cả dữ liệu đều từ thiết bị thực
3. **Consistency**: Tất cả thông số đều nhất quán với nhau
4. **Persistence**: Vân tay không đổi qua các session
5. **Geo-matching**: Timezone/locale phù hợp với proxy location

## 🧪 Testing

Chạy test để verify:
```bash
node test-antidetect.js
```

Test coverage:
- ✅ Persona loading
- ✅ Random selection
- ✅ Proxy integration
- ✅ Context options
- ✅ Spoofing script
- ✅ Database integration

## 🔄 Maintenance

### Cập nhật Personas
```bash
# Tạo personas mới
node scripts/generate-personas.js

# Test sau khi update
node test-antidetect.js
```

### Thêm User-Agents mới
Cập nhật `fingerprint-data.js` với UA mới từ Chrome releases.

### Thêm Regions mới
Cập nhật `TIMEZONE_BY_REGION` và `LANGUAGE_BY_REGION` trong `fingerprint-data.js`.

## 🎉 Kết quả

Hệ thống Antidetect đã được triển khai thành công với:

✅ **Giai đoạn 1**: Chuẩn bị dữ liệu và xây dựng Persona
✅ **Giai đoạn 2**: Triển khai logic tạo vân tay trong Backend  
✅ **Giai đoạn 3**: Tích hợp và lưu trữ trạng thái

Mỗi profile TikTok giờ đây có:
- Vân tay browser độc nhất và nhất quán
- Thông số phần cứng realistic
- Geolocation phù hợp với proxy
- WebGL/Canvas fingerprinting protection
- Persistence qua các session

Hệ thống sẵn sàng để sử dụng trong production! 🚀
