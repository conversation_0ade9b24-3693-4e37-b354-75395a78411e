const ProxyService = require('./src/services/ProxyService');

async function testProxyTypes() {
  const proxyService = new ProxyService();
  
  console.log('🧪 Testing different proxy types...\n');

  // Test cases for different proxy types
  const testCases = [
    {
      name: 'No Proxy',
      proxy: { type: 'No proxy' }
    },
    {
      name: 'HTTP Proxy (no auth)',
      proxy: {
        type: 'HTTP',
        host: '127.0.0.1',
        port: 8080
      }
    },
    {
      name: 'HTTP Proxy (with auth)',
      proxy: {
        type: 'HTTP',
        host: '127.0.0.1',
        port: 8080,
        username: 'user',
        password: 'pass'
      }
    },
    {
      name: 'HTTPS Proxy (no auth)',
      proxy: {
        type: 'HTTPS',
        host: '127.0.0.1',
        port: 8080
      }
    },
    {
      name: 'HTTPS Proxy (with auth)',
      proxy: {
        type: 'HTTPS',
        host: '127.0.0.1',
        port: 8080,
        username: 'user',
        password: 'pass'
      }
    },
    {
      name: 'SOCKS5 Proxy (no auth)',
      proxy: {
        type: 'SOCKS5',
        host: '127.0.0.1',
        port: 1080
      }
    },
    {
      name: 'SOCKS5 Proxy (with auth)',
      proxy: {
        type: 'SOCKS5',
        host: '127.0.0.1',
        port: 1080,
        username: 'user',
        password: 'pass'
      }
    },
    {
      name: 'SOCKS4 Proxy',
      proxy: {
        type: 'SOCKS4',
        host: '127.0.0.1',
        port: 1080
      }
    },
    {
      name: 'SSH Proxy',
      proxy: {
        type: 'SSH',
        host: '127.0.0.1',
        port: 22,
        username: 'user',
        password: 'pass'
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📋 Testing: ${testCase.name}`);
    console.log(`   Proxy: ${JSON.stringify(testCase.proxy, null, 2)}`);
    
    try {
      // Test proxy configuration creation
      if (testCase.proxy.type !== 'No proxy') {
        const config = proxyService.createProxyConfig(testCase.proxy);
        console.log(`   ✅ Config created: ${JSON.stringify(config, null, 2)}`);
      } else {
        console.log(`   ✅ No proxy configuration needed`);
      }

      // Test proxy connection (this will likely fail for non-existent proxies)
      console.log(`   🔄 Testing connection...`);
      const result = await proxyService.testProxyConnection(testCase.proxy, 'ip-api');
      
      if (result.isActive) {
        console.log(`   ✅ Connection successful!`);
        if (result.result) {
          console.log(`   📍 IP: ${result.result.ip || 'Unknown'}`);
          console.log(`   🌍 Country: ${result.result.country || 'Unknown'}`);
          console.log(`   🏙️  City: ${result.result.city || 'Unknown'}`);
        }
      } else {
        console.log(`   ❌ Connection failed: ${result.error || 'Unknown error'}`);
      }

    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
    
    console.log(`   ${'─'.repeat(50)}`);
  }

  console.log('\n🎯 Test completed!');
  console.log('\nNote: Connection tests may fail if proxy servers are not running locally.');
  console.log('The important part is that proxy configurations are created correctly.');
}

// Run the test
if (require.main === module) {
  testProxyTypes().catch(console.error);
}

module.exports = { testProxyTypes };
