#!/usr/bin/env node

/**
 * Test script cho hệ thống antidetect
 * Chạy: node test-antidetect.js
 */

const { chromium } = require('playwright');
const AntidetectManager = require('./src/antidetect/antidetect-manager');
const DatabaseManager = require('./src/database/manager');

async function testAntidetect() {
  console.log('🧪 Testing Antidetect System...\n');

  const antidetectManager = new AntidetectManager();
  const dbManager = new DatabaseManager();

  try {
    // 1. Test loading personas
    console.log('1. Loading personas...');
    await antidetectManager.loadPersonas();
    console.log(`✅ Loaded ${antidetectManager.personas.length} personas\n`);

    // 2. Test selecting random persona
    console.log('2. Selecting random persona...');
    const persona = await antidetectManager.selectRandomPersona('US');
    console.log(`✅ Selected persona: ${persona.id}`);
    console.log(`   Platform: ${persona.platform}`);
    console.log(`   User-Agent: ${persona.userAgent.substring(0, 50)}...`);
    console.log(`   Screen: ${persona.screen.width}x${persona.screen.height}`);
    console.log(`   WebGL Vendor: ${persona.webgl.vendor}`);
    console.log(`   Timezone: ${persona.timezone}\n`);

    // 3. Test proxy integration
    console.log('3. Testing proxy integration...');
    const testProxy = {
      host: '*************',
      port: 8080,
      username: null,
      password: null,
      country: 'US',
      city: 'New York'
    };

    const localeInfo = antidetectManager.generateLocaleFromProxy(testProxy);
    console.log(`✅ Generated locale from proxy:`);
    console.log(`   Timezone: ${localeInfo.timezone}`);
    console.log(`   Locale: ${localeInfo.locale}`);
    console.log(`   Languages: ${localeInfo.languages.join(', ')}\n`);

    // 4. Test context options
    console.log('4. Creating context options...');
    const contextOptions = antidetectManager.createContextOptions(persona, testProxy);
    console.log(`✅ Context options created:`);
    console.log(`   User-Agent: ${contextOptions.userAgent.substring(0, 50)}...`);
    console.log(`   Viewport: ${contextOptions.viewport.width}x${contextOptions.viewport.height}`);
    console.log(`   Locale: ${contextOptions.locale}`);
    console.log(`   Timezone: ${contextOptions.timezoneId}`);
    console.log(`   Geolocation: ${contextOptions.geolocation.latitude}, ${contextOptions.geolocation.longitude}\n`);

    // 5. Test spoofing script
    console.log('5. Testing spoofing script...');
    const spoofingScript = antidetectManager.createSpoofingScript(persona);
    console.log(`✅ Spoofing script generated (${spoofingScript.length} characters)\n`);

    // 6. Test browser launch (optional - comment out if you don't want to open browser)
    console.log('6. Testing browser launch...');
    const launchOptions = antidetectManager.createBrowserLaunchOptions();
    
    // Uncomment the following lines to test actual browser launch
    /*
    const browser = await chromium.launch({
      ...launchOptions,
      headless: false
    });

    const context = await browser.newContext(contextOptions);
    await context.addInitScript(spoofingScript);
    
    const page = await context.newPage();
    await page.goto('https://browserleaks.com/webgl');
    
    console.log('✅ Browser launched successfully!');
    console.log('   Check the browser window to see the fingerprint');
    console.log('   Press any key to close...');
    
    // Wait for user input
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.on('data', async () => {
      await browser.close();
      process.exit(0);
    });
    */

    console.log('✅ Browser launch options created\n');

    // 7. Test database integration
    console.log('7. Testing database integration...');
    await dbManager.initialize();
    
    // Create a test account with persona
    const testAccount = {
      id: 'test-account-123',
      username: 'testuser',
      password: 'testpass',
      status: 'not_logged_in',
      proxy: testProxy,
      persona: persona,
      stats: {
        followsToday: 0,
        followsThisSession: 0,
        lastActivity: null
      }
    };

    console.log(`✅ Test account created with persona ${persona.id}\n`);

    console.log('🎉 All tests passed! Antidetect system is working correctly.');
    console.log('\n📋 Summary:');
    console.log(`   - Personas loaded: ${antidetectManager.personas.length}`);
    console.log(`   - Test persona: ${persona.id} (${persona.platform})`);
    console.log(`   - WebGL vendor: ${persona.webgl.vendor}`);
    console.log(`   - Screen resolution: ${persona.screen.width}x${persona.screen.height}`);
    console.log(`   - Timezone: ${persona.timezone}`);
    console.log(`   - Proxy integration: ✅`);
    console.log(`   - Context options: ✅`);
    console.log(`   - Spoofing script: ✅`);
    console.log(`   - Database integration: ✅`);

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  testAntidetect().catch(console.error);
}

module.exports = { testAntidetect };
