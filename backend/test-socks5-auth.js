const ProxyService = require('./src/services/ProxyService');

async function testSocks5Auth() {
  const proxyService = new ProxyService();
  
  console.log('🧪 Testing SOCKS5 with authentication...\n');

  const socks5Proxy = {
    type: 'SOCKS5',
    host: '127.0.0.1',
    port: 1080,
    username: 'testuser',
    password: 'testpass',
    country: 'US',
    city: 'New York'
  };

  console.log('📋 Proxy configuration:');
  console.log(JSON.stringify(socks5Proxy, null, 2));

  try {
    console.log('\n🔄 Testing SOCKS5 proxy with authentication...');
    
    // This should trigger the special SOCKS5 auth handling
    const result = await proxyService.testProxyConnection(socks5Proxy, 'ip-api');
    
    console.log('\n📊 Test Result:');
    console.log(`   Active: ${result.isActive}`);
    console.log(`   Error: ${result.error || 'None'}`);
    
    if (result.result) {
      console.log('   Result data:');
      console.log(`     IP: ${result.result.ip || 'Unknown'}`);
      console.log(`     Country: ${result.result.country || 'Unknown'}`);
      console.log(`     City: ${result.result.city || 'Unknown'}`);
      console.log(`     ISP: ${result.result.isp || 'Unknown'}`);
    }

    if (result.isActive) {
      console.log('\n✅ SOCKS5 with auth test passed!');
    } else {
      console.log('\n❌ SOCKS5 with auth test failed, but this is expected for non-existent proxy');
    }

  } catch (error) {
    console.error('\n❌ Unexpected error:', error.message);
  }

  console.log('\n🎯 SOCKS5 authentication test completed!');
}

// Test direct method calls
async function testSocks5Methods() {
  const proxyService = new ProxyService();
  
  console.log('\n🔧 Testing SOCKS5 specific methods...\n');

  const socks5Proxy = {
    type: 'SOCKS5',
    host: '127.0.0.1',
    port: 1080,
    username: 'testuser',
    password: 'testpass'
  };

  try {
    console.log('1. Testing proxy connectivity...');
    const isConnectable = await proxyService.testProxyConnectivity(socks5Proxy);
    console.log(`   Connectivity result: ${isConnectable}`);

    console.log('\n2. Testing SOCKS5 connectivity...');
    const connectivityResult = await proxyService.testSocks5Connectivity(socks5Proxy, 'ip-api');
    console.log(`   SOCKS5 connectivity result:`, connectivityResult);

    console.log('\n3. Testing SOCKS5 with auth method...');
    const authResult = await proxyService.testSocks5ProxyWithAuth(socks5Proxy, 'ip-api');
    console.log(`   SOCKS5 auth result:`, authResult);

  } catch (error) {
    console.error('❌ Error in method testing:', error.message);
  }
}

// Run the tests
if (require.main === module) {
  testSocks5Auth()
    .then(() => testSocks5Methods())
    .catch(console.error);
}

module.exports = { testSocks5Auth, testSocks5Methods };
