# Proxy Support Documentation

## Overview
This backend now supports comprehensive proxy handling for all major proxy types, including proper handling of SOCKS5 proxies with authentication.

## Supported Proxy Types

### 1. No Proxy
- **Type**: `No proxy`
- **Description**: Direct connection without any proxy
- **Authentication**: Not applicable
- **Status**: ✅ Fully supported

### 2. HTTP Proxy
- **Type**: `HTTP`
- **Description**: Standard HTTP proxy
- **Authentication**: ✅ Username/Password supported
- **Status**: ✅ Fully supported
- **Example**:
  ```json
  {
    "type": "HTTP",
    "host": "proxy.example.com",
    "port": 8080,
    "username": "user",
    "password": "pass"
  }
  ```

### 3. HTTPS Proxy
- **Type**: `HTTPS`
- **Description**: HTTPS proxy for secure connections
- **Authentication**: ✅ Username/Password supported
- **Status**: ✅ Fully supported
- **Example**:
  ```json
  {
    "type": "HTTPS",
    "host": "proxy.example.com",
    "port": 8080,
    "username": "user",
    "password": "pass"
  }
  ```

### 4. SOCKS5 Proxy
- **Type**: `SOCKS5`
- **Description**: SOCKS5 proxy with special authentication handling
- **Authentication**: ⚠️ Special handling required
- **Status**: ✅ Supported with fallback method
- **Notes**: 
  - Without auth: Uses Playwright's native SOCKS5 support
  - With auth: Uses custom fallback method due to Playwright limitations
- **Example**:
  ```json
  {
    "type": "SOCKS5",
    "host": "proxy.example.com",
    "port": 1080,
    "username": "user",
    "password": "pass"
  }
  ```

### 5. SOCKS4 Proxy
- **Type**: `SOCKS4`
- **Description**: SOCKS4 proxy (no authentication)
- **Authentication**: ❌ Not supported by protocol
- **Status**: ✅ Fully supported
- **Example**:
  ```json
  {
    "type": "SOCKS4",
    "host": "proxy.example.com",
    "port": 1080
  }
  ```

### 6. SSH Proxy
- **Type**: `SSH`
- **Description**: SSH tunnel (fallback to HTTP)
- **Authentication**: ✅ Username/Password supported
- **Status**: ⚠️ Fallback implementation
- **Notes**: Currently treated as HTTP proxy, requires SSH tunnel setup externally
- **Example**:
  ```json
  {
    "type": "SSH",
    "host": "ssh.example.com",
    "port": 22,
    "username": "user",
    "password": "pass"
  }
  ```

## Technical Implementation

### ProxyService Class
The `ProxyService` class handles all proxy operations:

#### Key Methods:
- `testProxyConnection(proxy, ipChecker)`: Main proxy testing method
- `createProxyConfig(proxy)`: Creates Playwright-compatible proxy configuration
- `testSocks5ProxyWithAuth(proxy, ipChecker)`: Special handling for SOCKS5 with auth
- `testProxyConnectivity(proxy)`: Basic connectivity test
- `testSocks5Connectivity(proxy, ipChecker)`: SOCKS5-specific connectivity test

#### Error Handling:
- Automatic fallback for SOCKS5 authentication errors
- Graceful degradation for unsupported proxy types
- Comprehensive error reporting

### Integration Points:
1. **CommandRouter**: Handles WebSocket proxy test commands
2. **FollowInteractAutomation**: Uses proxy for browser automation
3. **AntidetectManager**: Integrates proxy with antidetect features

## Usage Examples

### Testing Proxy via WebSocket:
```javascript
// Send via WebSocket
{
  "command": "test_proxy",
  "data": {
    "proxy": {
      "type": "SOCKS5",
      "host": "127.0.0.1",
      "port": 1080,
      "username": "user",
      "password": "pass"
    },
    "ipChecker": "ip-api"
  }
}
```

### Response Format:
```javascript
{
  "type": "proxy_test_result",
  "isActive": true,
  "result": {
    "ip": "*************",
    "country": "United States",
    "city": "New York",
    "timezone": "America/New_York",
    "isp": "Example ISP"
  },
  "proxy": { /* original proxy config */ }
}
```

## Error Resolution

### Common Issues:

1. **SOCKS5 Authentication Error**:
   - **Error**: "Browser does not support socks5 proxy authentication"
   - **Solution**: Automatically handled by fallback method
   - **Result**: Basic connectivity test with limited IP information

2. **Proxy Connection Failed**:
   - **Error**: "net::ERR_PROXY_CONNECTION_FAILED"
   - **Cause**: Proxy server not reachable or incorrect configuration
   - **Solution**: Verify proxy server details and connectivity

3. **Authentication Failed**:
   - **Error**: Authentication-related errors
   - **Solution**: Verify username/password credentials

## Testing

Run the included test scripts:

```bash
# Test all proxy types
node test-proxy-types.js

# Test SOCKS5 authentication specifically
node test-socks5-auth.js
```

## Dependencies

- `playwright`: Browser automation with proxy support
- `socks-proxy-agent`: SOCKS5 proxy handling (installed but not yet fully utilized)
- `net`: Node.js networking for connectivity tests

## Future Improvements

1. **Enhanced SOCKS5 Support**: Implement full SOCKS5 client for better authentication handling
2. **SSH Tunnel Integration**: Add proper SSH tunnel creation and management
3. **Proxy Pool Management**: Support for rotating multiple proxies
4. **Performance Optimization**: Caching and connection pooling for proxy tests
5. **Advanced Error Recovery**: Automatic proxy failover and retry mechanisms
