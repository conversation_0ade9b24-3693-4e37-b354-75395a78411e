{"net": {"http_server_properties": {"servers": [{"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://libraweb-sg.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://sf16-website.neutral.ttwstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://sf16-sg.tiktokcdn.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://web-va.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://login-no1a.www.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://lf16-tiktok-common.ibytedtos.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://us.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://www.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://sf16-website-login.neutral.ttwstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398449345893436", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "network_stats": {"srtt": 27146}, "server": "https://storage.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398449442941850", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL2dvb2dsZXVzZXJjb250ZW50LmNvbQAAAA==", false, 0], "network_stats": {"srtt": 31977}, "server": "https://lh3.googleusercontent.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398449350221768", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://apis.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398449350016917", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398449482895385", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "network_stats": {"srtt": 42157}, "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398449483874721", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "network_stats": {"srtt": 30433}, "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398449484464161", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL2dzdGF0aWMuY29tAA==", false, 0], "network_stats": {"srtt": 26440}, "server": "https://encrypted-tbn0.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398449497475503", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 27027}, "server": "https://android.clients.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398449420725823", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://lh3.googleusercontent.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398449420626507", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 33310}, "server": "https://fonts.gstatic.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398449576120415", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 25931}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://mon.tiktokv.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://mcs-sg.tiktokv.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 39935}, "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", true, 0], "network_stats": {"srtt": 27484}, "server": "https://accounts.youtube.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 30337}, "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 28568}, "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 28503}, "server": "https://www.google.com"}], "supports_quic": {"address": "2606:4700:110:8041:7f54:ade7:c537:ff6a", "used_quic": true}, "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "4G"}}}